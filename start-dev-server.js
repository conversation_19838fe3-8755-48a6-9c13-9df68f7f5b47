const { spawn } = require('child_process');
const http = require('http');

console.log('🚀 Starting LibyanoEx Development Server...\n');

// Function to check if a port is available
function checkPort(port) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 2000
    }, (res) => {
      resolve(true); // Port is in use
    });

    req.on('error', () => resolve(false)); // Port is free
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function startServer() {
  // Try different ports
  const ports = [3000, 3001, 3002, 3004, 3005];
  
  for (const port of ports) {
    console.log(`🔍 Checking port ${port}...`);
    
    const isPortBusy = await checkPort(port);
    if (isPortBusy) {
      console.log(`⚠️  Port ${port} is already in use, trying next port...`);
      continue;
    }
    
    console.log(`✅ Port ${port} is available`);
    console.log(`🎯 Starting Next.js development server on port ${port}...\n`);
    
    const nextProcess = spawn('npx', ['next', 'dev', '--port', port.toString()], {
      stdio: 'inherit',
      shell: true
    });
    
    nextProcess.on('error', (err) => {
      console.error(`❌ Failed to start server on port ${port}:`, err.message);
    });
    
    nextProcess.on('close', (code) => {
      console.log(`🛑 Server process exited with code ${code}`);
    });
    
    // Wait a bit and check if server started successfully
    setTimeout(async () => {
      const isRunning = await checkPort(port);
      if (isRunning) {
        console.log(`\n🎉 SUCCESS! Server is running at:`);
        console.log(`   🏠 Homepage: http://localhost:${port}`);
        console.log(`   🔐 Login: http://localhost:${port}/auth/login`);
        console.log(`   📝 Register: http://localhost:${port}/auth/register`);
        console.log(`   📊 Dashboard: http://localhost:${port}/dashboard`);
        console.log(`\n💡 Press Ctrl+C to stop the server`);
      } else {
        console.log(`❌ Server failed to start on port ${port}`);
      }
    }, 8000);
    
    break;
  }
}

startServer().catch(console.error);
