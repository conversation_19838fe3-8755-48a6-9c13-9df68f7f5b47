// TEMPORARILY DISABLED - This file causes issues with next/headers in development
// This will be re-enabled when proper server component setup is configured

// import { createServerClient } from '@supabase/ssr'
// import { cookies } from 'next/headers'

export const createSupabaseServerClient = () => {
  // Temporarily return null to prevent next/headers issues
  console.warn('Supabase server client is temporarily disabled for development.')
  return null
}

// Original implementation commented out:
/*
export const createSupabaseServerClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  // Check if Supabase is properly configured
  if (!supabaseUrl || !supabaseAnonKey ||
      supabaseUrl === 'your_supabase_project_url' ||
      supabaseAnonKey === 'your_supabase_anon_key') {
    console.warn('Supabase is not configured. Returning null client.')
    return null
  }

  const cookieStore = cookies()

  return createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}
*/
