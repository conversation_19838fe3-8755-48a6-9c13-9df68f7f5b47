const http = require('http');

function checkServer(port) {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Server is running on port ${port}`);
      console.log(`Status: ${res.statusCode}`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ Server is NOT running on port ${port}`);
      console.log(`Error: ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`⏰ Timeout connecting to port ${port}`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function checkPorts() {
  console.log('Checking server status...');
  const ports = [3000, 3001, 3002, 3003];
  
  for (const port of ports) {
    await checkServer(port);
  }
}

checkPorts();
