Write-Host "Checking if Next.js development server is running..."

$ports = @(3000, 3001, 3002, 3003)

foreach ($port in $ports) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$port" -TimeoutSec 5 -ErrorAction Stop
        Write-Host "✅ Server is running on port $port" -ForegroundColor Green
        Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "Homepage: http://localhost:$port" -ForegroundColor Cyan
        Write-Host "Test page: http://localhost:$port/test" -ForegroundColor Cyan
        break
    }
    catch {
        Write-Host "❌ No server on port $port" -ForegroundColor Red
    }
}

Write-Host "Check complete."
