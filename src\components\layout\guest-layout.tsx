import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Package, ArrowLeft } from 'lucide-react'

interface GuestLayoutProps {
  children: React.ReactNode
  showBackToHome?: boolean
}

/**
 * Guest Layout Component
 * 
 * Simple layout for unauthenticated users accessing public pages
 */
export default function GuestLayout({ children, showBackToHome = true }: GuestLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Simple Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Package className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900 dark:text-white">LibyanoEx</span>
          </div>
          
          <div className="flex items-center space-x-4">
            {showBackToHome && (
              <Link href="/">
                <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to Home</span>
                </Button>
              </Link>
            )}
            <Link href="/auth/login">
              <Button variant="ghost" size="sm">Sign In</Button>
            </Link>
            <Link href="/auth/register">
              <Button size="sm">Create Account</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="py-6">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>

      {/* Simple Footer */}
      <footer className="bg-gray-900 text-white py-4 mt-auto">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-blue-600" />
              <span className="font-medium">LibyanoEx</span>
            </div>
            <div className="flex space-x-6 text-sm text-gray-400">
              <a href="https://libyanoex.com" className="hover:text-white transition-colors">Main Website</a>
              <a href="https://libyanoex.com/contact" className="hover:text-white transition-colors">Contact</a>
              <a href="https://libyanoex.com/help" className="hover:text-white transition-colors">Help</a>
            </div>
            <div className="text-sm text-gray-400">
              &copy; 2025 LibyanoEx. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
