import { PackageTracking } from '@/types'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import TrackingStatusComponent from './tracking-status'

interface TrackingPrintViewProps {
  package: PackageTracking
}

/**
 * Print-Friendly Tracking View
 * 
 * Optimized layout for printing tracking information
 */
export default function TrackingPrintView({ package: pkg }: TrackingPrintViewProps) {
  const formatAddress = () => {
    const addr = pkg.deliveryInfo.deliveryAddress
    return `${addr.name}\n${addr.street}\n${addr.city}, ${addr.state} ${addr.postalCode}\n${addr.country}`
  }

  const formatDimensions = () => {
    const { length, width, height, unit } = pkg.packageDetails.dimensions
    return `${length} × ${width} × ${height} ${unit}`
  }

  return (
    <div className="print:block hidden">
      <style jsx>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .print-content, .print-content * {
            visibility: visible;
          }
          .print-content {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          .no-print {
            display: none !important;
          }
        }
      `}</style>
      
      <div className="print-content p-8 bg-white text-black">
        {/* Header */}
        <div className="border-b-2 border-gray-300 pb-4 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold">LibyanoEx Package Tracking</h1>
              <p className="text-gray-600">Forwarding Service Report</p>
            </div>
            <div className="text-right">
              <p className="text-sm">Generated: {new Date().toLocaleString()}</p>
              <p className="text-sm">Tracking #: {pkg.trackingNumber}</p>
            </div>
          </div>
        </div>

        {/* Package Status */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Package Status</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><strong>Status:</strong> {pkg.statusDescription}</p>
              <p><strong>Estimated Delivery:</strong> {new Date(pkg.estimatedDelivery).toLocaleDateString()}</p>
              <p><strong>Last Updated:</strong> {new Date(pkg.updatedAt).toLocaleString()}</p>
            </div>
            <div>
              {pkg.referenceNumber && <p><strong>Reference:</strong> {pkg.referenceNumber}</p>}
              {pkg.orderId && <p><strong>Order ID:</strong> {pkg.orderId}</p>}
              <p><strong>Service:</strong> {pkg.packageDetails.serviceType}</p>
            </div>
          </div>
        </div>

        {/* Package Details */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Package Details</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><strong>Weight:</strong> {pkg.packageDetails.weight} {pkg.packageDetails.weightUnit}</p>
              <p><strong>Dimensions:</strong> {formatDimensions()}</p>
              <p><strong>Declared Value:</strong> {pkg.packageDetails.declaredValue} {pkg.packageDetails.currency}</p>
            </div>
            <div>
              <p><strong>Contents:</strong> {pkg.packageDetails.contents}</p>
              {pkg.packageDetails.specialInstructions && (
                <p><strong>Special Instructions:</strong> {pkg.packageDetails.specialInstructions}</p>
              )}
            </div>
          </div>
        </div>

        {/* Delivery Information */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Delivery Information</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><strong>Delivery Address:</strong></p>
              <pre className="text-sm whitespace-pre-line">{formatAddress()}</pre>
            </div>
            <div>
              <p><strong>Delivery Options:</strong></p>
              <ul className="text-sm">
                {pkg.deliveryInfo.requiresSignature && <li>• Signature Required</li>}
                {pkg.deliveryInfo.isResidential && <li>• Residential Delivery</li>}
                {pkg.deliveryInfo.deliveryInstructions && (
                  <li>• Instructions: {pkg.deliveryInfo.deliveryInstructions}</li>
                )}
              </ul>
            </div>
          </div>
        </div>

        {/* Tracking Timeline */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Tracking Timeline</h2>
          <div className="space-y-2">
            {pkg.events
              .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
              .map((event) => (
                <div key={event.id} className="border-l-2 border-gray-300 pl-4 pb-2">
                  <div className="flex justify-between">
                    <p className="font-medium">{event.title}</p>
                    <p className="text-sm text-gray-600">
                      {new Date(event.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <p className="text-sm text-gray-600">{event.description}</p>
                  <p className="text-xs text-gray-500">📍 {event.location}</p>
                </div>
              ))}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t-2 border-gray-300 pt-4 mt-6">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p><strong>LibyanoEx Forwarding Service</strong></p>
              <p>Email: <EMAIL></p>
              <p>Phone: +****************</p>
            </div>
            <div className="text-right">
              <p>For the most up-to-date tracking information,</p>
              <p>visit: app.libyanoex.com/packages/track</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
