# LibyanoEx Web Application System

A comprehensive business management platform built with modern web technologies. LibyanoEx provides a complete solution for transaction management, user administration, analytics, and business operations.

![LibyanoEx Dashboard](https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=LibyanoEx+Dashboard)

## 🚀 Features

### Core Business Functionality
- **Transaction Management** - Complete transaction processing with real-time updates
- **User Management** - Role-based access control with admin panel
- **Analytics Dashboard** - Business insights and performance metrics
- **Reports & Export** - Comprehensive reporting with data export capabilities
- **Multi-Currency Support** - Handle transactions in multiple currencies

### Technical Features
- **Modern UI/UX** - Responsive design with dark mode support
- **Real-time Updates** - Live data synchronization across users
- **Security First** - Row-level security and role-based permissions
- **Type Safety** - Full TypeScript implementation
- **Mobile Responsive** - Optimized for all device sizes

## 🛠️ Technology Stack

- **Frontend**: Next.js 14, React 19, TypeScript
- **Styling**: Tailwind CSS, Shadcn/ui Components
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with RLS
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod Validation
- **Charts**: Recharts
- **Icons**: Lucide React

## 🏃‍♂️ Quick Start

### Development Mode (No Setup Required)

```bash
# Clone the repository
git clone <repository-url>
cd libyanoex-web-app

# Install dependencies
npm install

# Start development server
npm run dev
```

Visit `http://localhost:3000` - The app runs with mock data and authentication for immediate development.

### Production Setup

For production deployment with real authentication and database:

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Get your project URL and API keys

2. **Configure Environment**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your Supabase credentials
   ```

3. **Set Up Database**
   - Run SQL scripts in Supabase SQL Editor:
     - `database/schema.sql`
     - `database/functions.sql`
     - `database/rls-policies.sql`
     - `database/seed-data.sql`

4. **Start Application**
   ```bash
   npm run dev
   ```

See [DEVELOPMENT_SETUP.md](./DEVELOPMENT_SETUP.md) for detailed setup instructions.

## 📱 Application Structure

### User Roles
- **Admin** - Full system access and user management
- **Manager** - Transaction oversight and reporting
- **User** - Standard transaction and account access
- **Client** - Limited access for external users

### Main Pages
- **Dashboard** - Overview with stats and quick actions
- **Transactions** - Create, view, and manage transactions
- **Users** - User management (admin/manager only)
- **Reports** - Analytics and data export
- **Settings** - Application configuration

## 🔒 Security Features

- **Row Level Security (RLS)** - Database-level access control
- **Role-Based Permissions** - Granular access management
- **Input Validation** - Comprehensive form validation
- **Audit Trails** - Complete transaction logging
- **Secure Authentication** - Supabase Auth integration

## 📊 Business Features

### Transaction Management
- Multiple transaction types (deposit, withdrawal, transfer, exchange)
- Real-time status updates
- Reference number generation
- Multi-currency support
- Fee calculation

### User Administration
- Role assignment and management
- Activity monitoring
- Account status control
- Bulk operations

### Analytics & Reporting
- Revenue analysis
- User activity metrics
- Transaction trends
- Data export (CSV, PDF)
- Custom date ranges

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Build the application
npm run build

# Deploy to Vercel
npx vercel --prod
```

### Other Platforms
The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🧪 Testing

```bash
# Run tests (when implemented)
npm run test

# Run linting
npm run lint

# Type checking
npm run type-check
```

## 📚 Documentation

- [Development Setup](./DEVELOPMENT_SETUP.md) - Detailed setup guide
- [Database Schema](./database/setup.md) - Database documentation
- [API Documentation](./docs/api.md) - API endpoints (when implemented)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the [Development Setup Guide](./DEVELOPMENT_SETUP.md)
- Review the [Database Documentation](./database/setup.md)
- Open an issue for bugs or feature requests

## 🎯 Roadmap

- [ ] Advanced analytics dashboard
- [ ] Mobile application
- [ ] API integrations
- [ ] Advanced reporting
- [ ] Multi-tenant support
- [ ] Automated testing suite
- [ ] Performance optimizations

---

Built with ❤️ for modern business management
