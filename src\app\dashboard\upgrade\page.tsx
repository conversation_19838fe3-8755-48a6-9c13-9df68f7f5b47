/**
 * Upgrade Page - Plan Upgrade Interface
 *
 * Allows users to upgrade from Basic plan to Standard or Premium plans
 *
 * Features:
 * - Plan comparison with current plan highlighted
 * - Clear upgrade benefits and pricing
 * - Freemium to paid conversion flow
 * - Payment integration (when implemented)
 */

'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import RoleBasedDashboardLayout from '@/components/layout/role-based-dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Package,
  Truck,
  Crown,
  Check,
  ArrowRight,
  Zap,
  TrendingUp,
  Clock,
  Phone,
  Camera,
  ShoppingCart
} from 'lucide-react'

export default function UpgradePage() {
  const { user } = useAuth()
  const [selectedPlan, setSelectedPlan] = useState<'premium'>('premium')

  // Current user plan (mock data)
  const currentPlan = 'basic'

  const plans = {
    basic: {
      name: 'Basic',
      price: 0,
      description: 'Your current plan',
      features: [
        'Up to 5 packages per month',
        'Basic package consolidation',
        'Standard shipping rates',
        'Email support',
        '15-day free storage',
        'Basic tracking'
      ],
      icon: Package,
      color: 'green'
    },
    premium: {
      name: 'Premium',
      price: 39.99,
      description: 'For high-volume shippers and businesses',
      features: [
        'Unlimited packages',
        'Premium consolidation service',
        'Best shipping rates (20% off)',
        'Phone & email support',
        '90-day free storage',
        'Package photos & inspection',
        'Personal shopper service',
        'Priority processing'
      ],
      upgradeFeatures: [
        'Unlimited packages',
        'Save 20% on all shipping',
        'Personal shopper service',
        'Phone support included',
        '6x longer storage period'
      ],
      icon: Crown,
      color: 'gold',
      popular: true
    }
  }

  const handleUpgrade = (planKey: 'premium') => {
    console.log(`Upgrading to ${planKey} plan`)
    // TODO: Implement payment flow
    alert(`Upgrade to ${plans[planKey].name} plan will be implemented with payment integration`)
  }

  return (
    <RoleBasedDashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Upgrade Your Plan
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Unlock more features and better rates with our paid plans
          </p>
        </div>

        {/* Current Plan Status */}
        <Card className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                  <Package className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-green-900 dark:text-green-100">
                    Current Plan: Basic (FREE)
                  </h3>
                  <p className="text-green-700 dark:text-green-200 text-sm">
                    You're using 3 of 5 packages this month
                  </p>
                </div>
              </div>
              <Badge className="bg-green-600">Active</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Plan Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(plans).map(([key, plan]) => {
            const PlanIcon = plan.icon
            const isCurrent = key === currentPlan
            const isSelected = key === selectedPlan
            const isPaid = key !== 'basic'

            return (
              <Card
                key={key}
                className={`relative ${
                  isCurrent
                    ? 'border-green-200 bg-green-50/50 dark:bg-green-900/10'
                    : isSelected && isPaid
                    ? 'ring-2 ring-blue-600 border-blue-600'
                    : ''
                } ${isPaid ? 'cursor-pointer hover:shadow-md transition-all' : ''}`}
                onClick={() => isPaid && setSelectedPlan(key as 'premium')}
              >
                {/* Plan Badge */}
                {isCurrent && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-green-600">
                    Current Plan
                  </Badge>
                )}
                {plan.popular && !isCurrent && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-600">
                    Most Popular
                  </Badge>
                )}

                <CardHeader className="text-center pb-2">
                  <div className="flex justify-center mb-2">
                    <PlanIcon className={`h-8 w-8 text-${plan.color}-600`} />
                  </div>
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white">
                    {key === 'basic' ? (
                      <span className="text-green-600">FREE</span>
                    ) : (
                      <>
                        ${plan.price}
                        <span className="text-sm font-normal text-gray-500">/month</span>
                      </>
                    )}
                  </div>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Features */}
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <Check className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>

                  {/* Upgrade Benefits */}
                  {plan.upgradeFeatures && (
                    <div className="border-t pt-3">
                      <p className="text-xs font-medium text-blue-600 mb-2">Upgrade Benefits:</p>
                      <ul className="space-y-1">
                        {plan.upgradeFeatures.map((benefit, index) => (
                          <li key={index} className="flex items-center text-xs text-blue-600">
                            <ArrowRight className="h-3 w-3 mr-1 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Action Button */}
                  {isCurrent ? (
                    <Button disabled className="w-full">
                      Current Plan
                    </Button>
                  ) : (
                    <Button
                      className="w-full"
                      variant={isSelected ? "default" : "outline"}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleUpgrade(key as 'premium')
                      }}
                    >
                      Upgrade to {plan.name}
                    </Button>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Upgrade Benefits Summary */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-900 dark:text-blue-100">
              <Zap className="h-5 w-5 mr-2" />
              Why Upgrade?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-start space-x-3">
                <TrendingUp className="h-6 w-6 text-blue-600 flex-shrink-0 mt-1" />
                <div>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">Better Rates</h4>
                  <p className="text-sm text-blue-700 dark:text-blue-200">Save 10-20% on shipping costs</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Package className="h-6 w-6 text-purple-600 flex-shrink-0 mt-1" />
                <div>
                  <h4 className="font-medium text-purple-900 dark:text-purple-100">Unlimited Packages</h4>
                  <p className="text-sm text-purple-700 dark:text-purple-200">Ship unlimited packages monthly</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Camera className="h-6 w-6 text-green-600 flex-shrink-0 mt-1" />
                <div>
                  <h4 className="font-medium text-green-900 dark:text-green-100">Package Photos</h4>
                  <p className="text-sm text-green-700 dark:text-green-200">Visual confirmation of packages</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Phone className="h-6 w-6 text-orange-600 flex-shrink-0 mt-1" />
                <div>
                  <h4 className="font-medium text-orange-900 dark:text-orange-100">Priority Support</h4>
                  <p className="text-sm text-orange-700 dark:text-orange-200">Phone and priority email support</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </RoleBasedDashboardLayout>
  )
}
