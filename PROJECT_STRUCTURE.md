# LibyanoEx Web App - Project Structure Guide

## 📁 Project Architecture Overview

This document provides a comprehensive guide to the LibyanoEx shipping forwarding web application structure. The project follows a modular, feature-based architecture for easy maintenance and scalability.

## 🏗️ Directory Structure

```
LibyanoEx Web App System/
├── 📁 src/
│   ├── 📁 app/                    # Next.js App Router pages
│   │   ├── 📁 (auth)/            # Authentication group routes
│   │   │   ├── 📁 login/         # Login page
│   │   │   └── 📁 register/      # Registration flow
│   │   ├── 📁 (dashboard)/       # Protected dashboard routes
│   │   │   ├── 📁 dashboard/     # Main dashboard
│   │   │   ├── 📁 packages/      # Package management
│   │   │   ├── 📁 addresses/     # Address book
│   │   │   ├── 📁 shipping/      # Shipping calculator
│   │   │   └── 📁 account/       # Account settings
│   │   ├── 📁 (admin)/           # Admin-only routes
│   │   │   ├── 📁 admin/         # Admin dashboard
│   │   │   ├── 📁 users/         # User management
│   │   │   └── 📁 reports/       # Analytics & reports
│   │   ├── 📁 api/               # API routes
│   │   │   ├── 📁 auth/          # Authentication endpoints
│   │   │   ├── 📁 packages/      # Package API
│   │   │   ├── 📁 shipping/      # Shipping calculations
│   │   │   └── 📁 webhooks/      # External service webhooks
│   │   ├── layout.tsx            # Root layout
│   │   ├── page.tsx              # Home page (auth landing)
│   │   └── globals.css           # Global styles
│   ├── 📁 components/            # Reusable UI components
│   │   ├── 📁 ui/                # Base UI components (shadcn/ui)
│   │   ├── 📁 auth/              # Authentication components
│   │   ├── 📁 dashboard/         # Dashboard-specific components
│   │   ├── 📁 shipping/          # Shipping-related components
│   │   ├── 📁 forms/             # Form components
│   │   └── 📁 layout/            # Layout components
│   ├── 📁 features/              # Feature-based modules
│   │   ├── 📁 authentication/    # Auth logic & components
│   │   ├── 📁 packages/          # Package management
│   │   ├── 📁 shipping/          # Shipping calculations
│   │   ├── 📁 addresses/         # Address management
│   │   ├── 📁 payments/          # Payment processing
│   │   └── 📁 notifications/     # Notification system
│   ├── 📁 lib/                   # Utility libraries
│   │   ├── 📁 supabase/          # Database client & types
│   │   ├── 📁 validations/       # Zod schemas
│   │   ├── 📁 utils/             # Helper functions
│   │   └── 📁 constants/         # App constants
│   ├── 📁 hooks/                 # Custom React hooks
│   ├── 📁 stores/                # Zustand state stores
│   ├── 📁 types/                 # TypeScript type definitions
│   └── middleware.ts             # Next.js middleware
├── 📁 database/                  # Database schema & migrations
├── 📁 docs/                      # Documentation
└── 📁 public/                    # Static assets
```

## 🎯 Feature Modules

### Authentication (`src/features/authentication/`)
- User registration with multi-step process
- Login/logout functionality
- Password reset
- Email verification
- Role-based access control

### Packages (`src/features/packages/`)
- Package creation and tracking
- Status updates
- Photo uploads
- Package consolidation
- Shipping requests

### Shipping (`src/features/shipping/`)
- Rate calculations
- Carrier integrations
- Label generation
- Tracking integration
- Delivery notifications

### Addresses (`src/features/addresses/`)
- Address book management
- Address validation
- Default shipping addresses
- International address formats

## 🔧 Component Organization

### UI Components (`src/components/ui/`)
Base components from shadcn/ui library:
- Button, Input, Card, Dialog, etc.
- Consistent styling and behavior
- Fully accessible

### Feature Components (`src/components/[feature]/`)
Feature-specific components:
- Organized by functionality
- Self-contained with props interface
- Documented with JSDoc comments

## 📊 State Management

### Zustand Stores (`src/stores/`)
- `authStore.ts` - Authentication state
- `packageStore.ts` - Package management
- `shippingStore.ts` - Shipping calculations
- `notificationStore.ts` - App notifications

## 🛠️ Utilities & Libraries

### Database (`src/lib/supabase/`)
- Client configuration
- Type definitions
- Query helpers
- Real-time subscriptions

### Validations (`src/lib/validations/`)
- Zod schemas for forms
- API request validation
- Type-safe data validation

## 📝 Naming Conventions

### Files & Directories
- **kebab-case** for directories: `user-management/`
- **PascalCase** for components: `UserProfile.tsx`
- **camelCase** for utilities: `formatCurrency.ts`
- **UPPER_CASE** for constants: `API_ENDPOINTS.ts`

### Components
- **PascalCase** for component names: `PackageCard`
- **camelCase** for props: `isLoading`, `onSubmit`
- **Descriptive names**: `ShippingCalculatorForm` vs `Form`

### Functions
- **Verb-based naming**: `getUserPackages()`, `calculateShipping()`
- **Boolean prefixes**: `isLoading`, `hasPermission`, `canEdit`

## 🔍 Code Documentation Standards

### Component Documentation
```typescript
/**
 * PackageCard - Displays package information with status and actions
 * 
 * @param package - Package data object
 * @param onEdit - Callback when edit button is clicked
 * @param onTrack - Callback when track button is clicked
 * @returns JSX element representing a package card
 */
```

### Function Documentation
```typescript
/**
 * Calculates shipping cost based on package dimensions and destination
 * 
 * @param weight - Package weight in pounds
 * @param dimensions - Package dimensions object
 * @param destination - Shipping destination address
 * @returns Promise resolving to shipping cost calculation
 */
```

## 🚀 Getting Started

1. **Review this structure** - Understand the organization
2. **Check feature modules** - Each feature is self-contained
3. **Follow naming conventions** - Consistent naming across the app
4. **Document new components** - Add JSDoc comments
5. **Use TypeScript** - Leverage type safety throughout

## 📚 Key Files to Know

- `src/app/layout.tsx` - Root application layout
- `src/app/page.tsx` - Home page (authentication landing)
- `src/middleware.ts` - Route protection and redirects
- `src/lib/supabase/client.ts` - Database client
- `PROJECT_STRUCTURE.md` - This file (project guide)

---

This structure ensures maintainability, scalability, and developer-friendly organization for the LibyanoEx shipping forwarding application.
