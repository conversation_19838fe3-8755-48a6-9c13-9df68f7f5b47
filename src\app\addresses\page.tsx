/**
 * Address Book Page - Shipping Address Management
 * 
 * Comprehensive address management system for LibyanoEx shipping portal
 * 
 * Features:
 * - Address listing with search and filtering
 * - Add, edit, and delete addresses
 * - Set default shipping addresses
 * - Address validation
 * - Import/export functionality
 * - Address categories (Home, Work, Business)
 * - Quick address selection for shipping
 * 
 * Components:
 * - AddressList - Main address listing
 * - AddressCard - Individual address display
 * - AddressForm - Add/edit address form
 * - AddressActions - Bulk operations
 */

'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  MapPin, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Star, 
  Home, 
  Building, 
  User,
  Phone,
  Mail,
  CheckCircle,
  MoreVertical,
  Download,
  Upload
} from 'lucide-react'

// Address types and interfaces
type AddressType = 'home' | 'work' | 'business' | 'other'

interface Address {
  id: string
  label: string
  type: AddressType
  isDefault: boolean
  recipient: {
    name: string
    company?: string
    phone: string
    email: string
  }
  address: {
    street: string
    street2?: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  createdAt: string
  lastUsed?: string
}

export default function AddressBookPage() {
  const { user } = useAuth()
  
  // State management
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<AddressType | 'all'>('all')
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingAddress, setEditingAddress] = useState<Address | null>(null)
  const [selectedAddresses, setSelectedAddresses] = useState<string[]>([])

  // Mock address data - in real app, this would come from API
  const mockAddresses: Address[] = [
    {
      id: 'addr-001',
      label: 'Home Address',
      type: 'home',
      isDefault: true,
      recipient: {
        name: 'John Doe',
        phone: '+****************',
        email: '<EMAIL>'
      },
      address: {
        street: '123 Main Street',
        street2: 'Apt 4B',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'United States'
      },
      createdAt: '2025-01-01',
      lastUsed: '2025-01-10'
    },
    {
      id: 'addr-002',
      label: 'Office',
      type: 'work',
      isDefault: false,
      recipient: {
        name: 'John Doe',
        company: 'Tech Corp Inc.',
        phone: '+****************',
        email: '<EMAIL>'
      },
      address: {
        street: '456 Business Ave',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'United States'
      },
      createdAt: '2025-01-02',
      lastUsed: '2025-01-08'
    },
    {
      id: 'addr-003',
      label: 'Mom\'s House',
      type: 'home',
      isDefault: false,
      recipient: {
        name: 'Mary Doe',
        phone: '+****************',
        email: '<EMAIL>'
      },
      address: {
        street: '789 Family Lane',
        city: 'Chicago',
        state: 'IL',
        zipCode: '60601',
        country: 'United States'
      },
      createdAt: '2025-01-03'
    },
    {
      id: 'addr-004',
      label: 'Warehouse',
      type: 'business',
      isDefault: false,
      recipient: {
        name: 'Shipping Department',
        company: 'Storage Solutions LLC',
        phone: '+****************',
        email: '<EMAIL>'
      },
      address: {
        street: '321 Industrial Blvd',
        city: 'Miami',
        state: 'FL',
        zipCode: '33101',
        country: 'United States'
      },
      createdAt: '2025-01-04',
      lastUsed: '2025-01-09'
    }
  ]

  /**
   * Filters addresses based on search and type
   */
  const filteredAddresses = mockAddresses.filter(address => {
    const matchesSearch = searchQuery === '' || 
      address.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      address.recipient.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      address.address.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
      address.address.state.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesType = selectedType === 'all' || address.type === selectedType
    
    return matchesSearch && matchesType
  })

  /**
   * Gets the appropriate icon for address type
   */
  const getAddressTypeIcon = (type: AddressType) => {
    const icons = {
      home: Home,
      work: Building,
      business: Building,
      other: MapPin
    }
    return icons[type] || MapPin
  }

  /**
   * Gets the appropriate color for address type
   */
  const getAddressTypeColor = (type: AddressType): string => {
    const colors = {
      home: 'text-blue-600',
      work: 'text-purple-600',
      business: 'text-green-600',
      other: 'text-gray-600'
    }
    return colors[type] || 'text-gray-600'
  }

  /**
   * Handles setting default address
   */
  const handleSetDefault = (addressId: string) => {
    // TODO: Implement set default functionality
    console.log('Setting default address:', addressId)
  }

  /**
   * Handles address editing
   */
  const handleEditAddress = (address: Address) => {
    setEditingAddress(address)
    setShowAddForm(true)
  }

  /**
   * Handles address deletion
   */
  const handleDeleteAddress = (addressId: string) => {
    if (confirm('Are you sure you want to delete this address?')) {
      // TODO: Implement delete functionality
      console.log('Deleting address:', addressId)
    }
  }

  /**
   * Handles bulk operations
   */
  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on addresses:`, selectedAddresses)
    // TODO: Implement bulk operations
    setSelectedAddresses([])
  }

  /**
   * Formats address for display
   */
  const formatAddress = (address: Address['address']): string => {
    const parts = [
      address.street,
      address.street2,
      `${address.city}, ${address.state} ${address.zipCode}`,
      address.country
    ].filter(Boolean)
    
    return parts.join('\n')
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Address Book
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your shipping addresses for quick and easy ordering
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center space-x-2">
            <Button variant="outline" onClick={() => handleBulkAction('export')}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" onClick={() => handleBulkAction('import')}>
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Address
            </Button>
          </div>
        </div>

        {/* Address Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {mockAddresses.length}
                  </p>
                  <p className="text-xs text-gray-500">Total Addresses</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Home className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {mockAddresses.filter(a => a.type === 'home').length}
                  </p>
                  <p className="text-xs text-gray-500">Home</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {mockAddresses.filter(a => a.type === 'work' || a.type === 'business').length}
                  </p>
                  <p className="text-xs text-gray-500">Business</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Star className="h-4 w-4 text-yellow-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {mockAddresses.filter(a => a.isDefault).length}
                  </p>
                  <p className="text-xs text-gray-500">Default</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5 text-blue-600" />
              <span>Search & Filter</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search addresses, names, cities..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Type Filter */}
              <div className="md:w-48">
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value as AddressType | 'all')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800"
                >
                  <option value="all">All Types</option>
                  <option value="home">Home</option>
                  <option value="work">Work</option>
                  <option value="business">Business</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedAddresses.length > 0 && (
          <Card>
            <CardContent className="py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">
                    {selectedAddresses.length} selected
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('export')}>
                    Export Selected
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('delete')}>
                    Delete Selected
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setSelectedAddresses([])}>
                    Clear Selection
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Address List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Addresses ({filteredAddresses.length})
            </h2>
          </div>

          {filteredAddresses.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No addresses found
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {searchQuery || selectedType !== 'all'
                    ? 'Try adjusting your search or filters.'
                    : 'Add your first shipping address to get started.'}
                </p>
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Address
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAddresses.map((address) => {
                const TypeIcon = getAddressTypeIcon(address.type)

                return (
                  <Card
                    key={address.id}
                    className={`hover:shadow-md transition-shadow ${
                      address.isDefault ? 'ring-2 ring-blue-500 border-blue-500' : ''
                    }`}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          <TypeIcon className={`h-5 w-5 ${getAddressTypeColor(address.type)}`} />
                          <div>
                            <CardTitle className="text-lg">{address.label}</CardTitle>
                            {address.isDefault && (
                              <Badge variant="default" className="mt-1">
                                <Star className="h-3 w-3 mr-1" />
                                Default
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditAddress(address)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteAddress(address.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        {/* Recipient Info */}
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <User className="h-4 w-4 text-gray-500" />
                            <span className="font-medium text-gray-900 dark:text-white">
                              {address.recipient.name}
                            </span>
                          </div>
                          {address.recipient.company && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 ml-6">
                              {address.recipient.company}
                            </p>
                          )}
                        </div>

                        {/* Address */}
                        <div>
                          <div className="flex items-start space-x-2">
                            <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              <p>{address.address.street}</p>
                              {address.address.street2 && <p>{address.address.street2}</p>}
                              <p>
                                {address.address.city}, {address.address.state} {address.address.zipCode}
                              </p>
                              <p>{address.address.country}</p>
                            </div>
                          </div>
                        </div>

                        {/* Contact Info */}
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <Phone className="h-3 w-3 text-gray-500" />
                            <span className="text-xs text-gray-600 dark:text-gray-300">
                              {address.recipient.phone}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Mail className="h-3 w-3 text-gray-500" />
                            <span className="text-xs text-gray-600 dark:text-gray-300">
                              {address.recipient.email}
                            </span>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
                          {!address.isDefault && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSetDefault(address.id)}
                            >
                              <Star className="h-3 w-3 mr-1" />
                              Set Default
                            </Button>
                          )}

                          {address.lastUsed && (
                            <span className="text-xs text-gray-500">
                              Last used: {new Date(address.lastUsed).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>

        {/* Add/Edit Address Dialog */}
        {showAddForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <CardHeader>
                <CardTitle>
                  {editingAddress ? 'Edit Address' : 'Add New Address'}
                </CardTitle>
                <CardDescription>
                  {editingAddress
                    ? 'Update the address information below'
                    : 'Add a new shipping address to your address book'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Address form will be implemented in the next phase with full validation and address verification.
                  </p>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => {
                        setShowAddForm(false)
                        setEditingAddress(null)
                      }}
                      className="flex-1"
                    >
                      Close
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
