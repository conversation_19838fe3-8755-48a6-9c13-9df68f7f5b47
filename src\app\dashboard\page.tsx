/**
 * Dashboard Page - Shipping Portal Main Dashboard
 *
 * Main dashboard for LibyanoEx shipping forwarding portal users
 *
 * Features:
 * - Package statistics and overview
 * - Recent package activity
 * - Quick action buttons for common tasks
 * - Shipping metrics and analytics
 * - Responsive design optimized for shipping operations
 * - Integration with authentication system
 */

'use client'

import { useAuth } from '@/hooks/use-auth'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { PackageCard } from '@/components/shipping/package-card'
import Link from 'next/link'
import {
  Package,
  Truck,
  MapPin,
  Clock,
  Plus,
  Calculator,
  BookOpen,
  Settings,
  TrendingUp,
  Globe,
  Crown,
  ArrowRight,
  Zap
} from 'lucide-react'

export default function DashboardPage() {
  const { user } = useAuth()

  // Mock user plan data - in real app, this would come from user profile
  const userPlan = 'basic' // This would be fetched from user data
  const isBasicPlan = userPlan === 'basic'

  // Mock shipping data - in real app, this would come from API
  const shippingStats = [
    {
      title: 'Active Packages',
      value: '12',
      change: '+3 this week',
      changeType: 'positive' as const,
      icon: Package,
      color: 'blue'
    },
    {
      title: 'In Transit',
      value: '8',
      change: '2 arriving today',
      changeType: 'positive' as const,
      icon: Truck,
      color: 'purple'
    },
    {
      title: 'Delivered',
      value: '47',
      change: '+5 this month',
      changeType: 'positive' as const,
      icon: MapPin,
      color: 'green'
    },
    {
      title: 'Avg. Delivery',
      value: '5.2 days',
      change: '0.3 days faster',
      changeType: 'positive' as const,
      icon: Clock,
      color: 'orange'
    },
  ]

  // Mock recent packages data
  const recentPackages = [
    {
      id: 'pkg-001',
      trackingNumber: 'LBX-2025-001234',
      description: 'Electronics - iPhone 15 Pro',
      status: 'in-transit' as const,
      destination: 'New York, NY',
      weight: 1.2,
      createdAt: '2025-01-10',
      estimatedDelivery: '2025-01-15',
      carrier: 'DHL Express'
    },
    {
      id: 'pkg-002',
      trackingNumber: 'LBX-2025-001235',
      description: 'Clothing - Winter Jacket',
      status: 'processing' as const,
      destination: 'Los Angeles, CA',
      weight: 2.5,
      createdAt: '2025-01-09',
      estimatedDelivery: '2025-01-16',
      carrier: 'FedEx'
    },
    {
      id: 'pkg-003',
      trackingNumber: 'LBX-2025-001236',
      description: 'Books - Programming Collection',
      status: 'delivered' as const,
      destination: 'Chicago, IL',
      weight: 3.8,
      createdAt: '2025-01-08',
      estimatedDelivery: '2025-01-12',
      carrier: 'UPS'
    },
  ]

  /**
   * Handles package tracking
   */
  const handleTrackPackage = (trackingNumber: string) => {
    // TODO: Implement tracking functionality
    console.log('Tracking package:', trackingNumber)
  }

  /**
   * Handles package editing
   */
  const handleEditPackage = (packageId: string) => {
    // TODO: Navigate to package edit page
    console.log('Editing package:', packageId)
  }

  /**
   * Handles viewing package details
   */
  const handleViewPackageDetails = (packageId: string) => {
    // TODO: Navigate to package details page
    console.log('Viewing package details:', packageId)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Welcome back, {user?.full_name || user?.email}!
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Track your packages and manage your shipments from your LibyanoEx portal.
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Button className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>New Package</span>
            </Button>
          </div>
        </div>

        {/* Upgrade Banner for Basic Plan Users */}
        {isBasicPlan && (
          <Card className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-purple-200 dark:border-purple-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                      <Crown className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-1">
                      Unlock More with Premium Plans
                    </h3>
                    <p className="text-purple-700 dark:text-purple-200 text-sm mb-3">
                      You're currently on the Basic plan (5 packages/month). Upgrade for more packages, better rates, and premium features.
                    </p>
                    <div className="flex flex-wrap gap-4 text-sm">
                      <div className="flex items-center text-purple-600 dark:text-purple-300">
                        <Zap className="h-4 w-4 mr-1" />
                        <span>Up to 50 packages/month</span>
                      </div>
                      <div className="flex items-center text-purple-600 dark:text-purple-300">
                        <TrendingUp className="h-4 w-4 mr-1" />
                        <span>10-20% shipping discounts</span>
                      </div>
                      <div className="flex items-center text-purple-600 dark:text-purple-300">
                        <Package className="h-4 w-4 mr-1" />
                        <span>Package photos included</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <Button asChild className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white">
                    <Link href="/dashboard/upgrade">
                      <span>Upgrade Now</span>
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Plan Usage Stats for Basic Users */}
        {isBasicPlan && (
          <Card className="border-orange-200 dark:border-orange-800">
            <CardHeader>
              <CardTitle className="flex items-center text-orange-900 dark:text-orange-100">
                <Package className="h-5 w-5 mr-2" />
                Your Basic Plan Usage
              </CardTitle>
              <CardDescription>
                Track your monthly package allowance and see when you might need to upgrade
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Packages this month</span>
                    <span className="font-medium">3 of 5 used</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-orange-600 h-2 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                </div>
                <div className="flex justify-between items-center pt-2 border-t">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    2 packages remaining this month
                  </span>
                  <Button asChild variant="outline" size="sm" className="text-orange-600 border-orange-600 hover:bg-orange-50">
                    <Link href="/dashboard/upgrade">
                      View Upgrade Options
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Shipping Stats Grid */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {shippingStats.map((stat) => {
            const Icon = stat.icon
            return (
              <Card key={stat.title} className="hover:shadow-md transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.title}
                  </CardTitle>
                  <div className={`p-2 rounded-lg bg-${stat.color}-100 dark:bg-${stat.color}-900/20`}>
                    <Icon className={`h-4 w-4 text-${stat.color}-600 dark:text-${stat.color}-400`} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span className={stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}>
                      {stat.change}
                    </span>
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Quick Actions</span>
            </CardTitle>
            <CardDescription>
              Common shipping tasks and tools
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <Button className="h-20 flex-col space-y-2 bg-blue-600 hover:bg-blue-700">
                <Plus className="h-6 w-6" />
                <span>New Package</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2 hover:bg-purple-50 dark:hover:bg-purple-900/20">
                <Calculator className="h-6 w-6 text-purple-600" />
                <span>Shipping Calculator</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2 hover:bg-green-50 dark:hover:bg-green-900/20">
                <BookOpen className="h-6 w-6 text-green-600" />
                <span>Address Book</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2 hover:bg-orange-50 dark:hover:bg-orange-900/20">
                <Settings className="h-6 w-6 text-orange-600" />
                <span>Account Settings</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Packages */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5 text-blue-600" />
                  <span>Recent Packages</span>
                </CardTitle>
                <CardDescription>
                  Your latest package activity and shipments
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                View All Packages
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentPackages.map((pkg) => (
                <PackageCard
                  key={pkg.id}
                  package={pkg}
                  onTrack={handleTrackPackage}
                  onEdit={handleEditPackage}
                  onViewDetails={handleViewPackageDetails}
                />
              ))}
            </div>

            {recentPackages.length === 0 && (
              <div className="text-center py-8">
                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No packages yet
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Start by creating your first package shipment
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Package
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Shipping Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-green-600" />
                <span>Shipping Tips</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Package consolidation can save up to 60% on shipping costs
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Use our shipping calculator to compare rates across carriers
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Add package photos for better protection and tracking
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-blue-600" />
                <span>Account Status</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">Subscription Plan</span>
                  <Badge variant="default" className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    Standard
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">Storage Used</span>
                  <span className="text-sm font-medium">15 / 60 days</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">Monthly Packages</span>
                  <span className="text-sm font-medium">12 / 50</span>
                </div>
                <Button variant="outline" size="sm" className="w-full">
                  Upgrade Plan
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
