'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Search, Package, ArrowRight } from 'lucide-react'

/**
 * Guest Package Tracking Component
 * 
 * Allows users to track packages without authentication
 * Redirects to the main tracking page with pre-populated search
 */
export default function GuestTracking() {
  const [trackingNumber, setTrackingNumber] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [mounted, setMounted] = useState(false)
  const router = useRouter()

  // Ensure component is mounted on client side to prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  const handleTrack = async () => {
    if (!trackingNumber.trim()) return

    setIsLoading(true)
    
    // Simulate brief loading for better UX
    setTimeout(() => {
      // Redirect to tracking page with the tracking number as a URL parameter
      const encodedTrackingNumber = encodeURIComponent(trackingNumber.trim())
      router.push(`/packages/track?q=${encodedTrackingNumber}`)
    }, 500)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTrack()
    }
  }

  // Show loading state during SSR and initial client mount
  if (!mounted) {
    return (
      <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-2 border-blue-200 dark:border-blue-800 shadow-lg">
        <CardContent className="p-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-3">
              <Package className="h-8 w-8 text-blue-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Track Your Package
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Loading tracking interface...
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div suppressHydrationWarning>
      <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-2 border-blue-200 dark:border-blue-800 shadow-lg">
        <CardContent className="p-6">
        <div className="text-center mb-6">
          <div className="flex items-center justify-center mb-3">
            <Package className="h-8 w-8 text-blue-600 mr-2" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              Track Your Package
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-300 text-sm">
            Track your package without logging in
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex space-x-2">
            <div className="flex-1">
              <Input
                placeholder="Enter tracking number, reference number, or order ID..."
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                onKeyPress={handleKeyPress}
                className="text-base h-12 border-2 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400"
                disabled={isLoading}
                suppressHydrationWarning
              />
            </div>
            <Button
              onClick={handleTrack}
              disabled={isLoading || !trackingNumber.trim()}
              className="h-12 px-6 bg-blue-600 hover:bg-blue-700 text-white"
              suppressHydrationWarning
            >
              {isLoading ? (
                <Search className="h-5 w-5 animate-spin" />
              ) : (
                <>
                  <Search className="h-5 w-5 mr-2" />
                  Track
                </>
              )}
            </Button>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
              Example formats: LBX123456789, REF-2024-001, ORD-789123
            </p>
            <div className="flex items-center justify-center text-xs text-blue-600 dark:text-blue-400">
              <span>No account required</span>
              <ArrowRight className="h-3 w-3 ml-1" />
            </div>
          </div>
        </div>

        {/* Quick Access Links */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row gap-2 text-center">
            <div className="flex-1">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                Need an account?
              </p>
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs"
                onClick={() => router.push('/auth/register')}
                suppressHydrationWarning
              >
                Create Account
              </Button>
            </div>
            <div className="flex-1">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                Already have one?
              </p>
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs"
                onClick={() => router.push('/auth/login')}
                suppressHydrationWarning
              >
                Sign In
              </Button>
            </div>
          </div>
        </div>
        </CardContent>
      </Card>
    </div>
  )
}
