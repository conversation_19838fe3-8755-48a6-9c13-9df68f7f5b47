import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Package Tracking - LibyanoEx Forwarding Service',
  description: 'Track your packages in real-time with LibyanoEx. Get detailed status updates, delivery estimates, and shipment timeline for your forwarded packages.',
  keywords: [
    'package tracking',
    'shipment tracking', 
    'LibyanoEx tracking',
    'forwarding service',
    'package status',
    'delivery tracking',
    'shipping updates'
  ],
  openGraph: {
    title: 'Package Tracking - LibyanoEx',
    description: 'Track your packages in real-time with detailed status updates and delivery information.',
    type: 'website',
  },
  robots: {
    index: true,
    follow: true,
  }
}

export default function TrackingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
