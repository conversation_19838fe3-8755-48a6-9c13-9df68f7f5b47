/**
 * User Management Page - Enhanced Shipping Company User Control
 *
 * Comprehensive user management interface for LibyanoEx shipping portal admins
 *
 * Features:
 * - Enhanced user listing with shipping-specific data
 * - Subscription plan management
 * - Package count and spending tracking
 * - Advanced filtering and search
 * - Bulk operations for user management
 * - User activity monitoring
 * - Shipping-focused user metrics
 */

'use client'

import { useState, useMemo } from 'react'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { useRequireRole } from '@/hooks/use-auth'
import { formatDate, getInitials } from '@/lib/utils'
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  UserPlus,
  Mail,
  Phone,
  Calendar,
  Package,
  DollarSign,
  Crown,
  Shield,
  Edit,
  Trash2,
  Download,
  RefreshCw,
  Eye,
  Ban,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

// Enhanced user data for shipping company - in real app, this would come from API
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    fullName: 'Sarah <PERSON>',
    role: 'customer',
    phone: '+****************',
    company: 'Personal',
    isActive: true,
    subscriptionPlan: 'premium',
    packagesCount: 47,
    totalSpent: 2340.50,
    isVerified: true,
    createdAt: '2024-12-01T10:00:00Z',
    lastLogin: '2025-01-13T09:30:00Z',
    address: { city: 'New York', state: 'NY', country: 'USA' },
    notes: 'VIP customer, excellent payment history'
  },
  {
    id: '2',
    email: '<EMAIL>',
    fullName: 'Michael Chen',
    role: 'customer',
    phone: '+****************',
    company: 'Tech Solutions Inc',
    isActive: true,
    subscriptionPlan: 'standard',
    packagesCount: 32,
    totalSpent: 1890.25,
    isVerified: true,
    createdAt: '2024-11-15T14:20:00Z',
    lastLogin: '2025-01-12T16:45:00Z',
    address: { city: 'Los Angeles', state: 'CA', country: 'USA' }
  },
  {
    id: '3',
    email: '<EMAIL>',
    fullName: 'System Administrator',
    role: 'admin',
    phone: '+****************',
    company: 'LibyanoEx',
    isActive: true,
    subscriptionPlan: 'none',
    packagesCount: 0,
    totalSpent: 0,
    isVerified: true,
    createdAt: '2024-10-01T08:00:00Z',
    lastLogin: '2025-01-13T11:00:00Z',
    address: { city: 'Miami', state: 'FL', country: 'USA' }
  },
  {
    id: '4',
    email: '<EMAIL>',
    fullName: 'Emma Wilson',
    role: 'customer',
    phone: '+****************',
    company: 'Personal',
    isActive: false,
    subscriptionPlan: 'basic',
    packagesCount: 28,
    totalSpent: 1567.80,
    isVerified: false,
    createdAt: '2024-12-20T12:30:00Z',
    lastLogin: '2024-12-25T10:15:00Z',
    address: { city: 'Chicago', state: 'IL', country: 'USA' },
    notes: 'Needs email verification reminder'
  },
  {
    id: '5',
    email: '<EMAIL>',
    fullName: 'David Rodriguez',
    role: 'customer',
    phone: '+****************',
    company: 'Rodriguez Imports',
    isActive: true,
    subscriptionPlan: 'premium',
    packagesCount: 25,
    totalSpent: 1234.75,
    isVerified: true,
    createdAt: '2024-09-12T08:00:00Z',
    lastLogin: '2025-01-11T14:30:00Z',
    address: { city: 'Miami', state: 'FL', country: 'USA' },
    notes: 'Business customer, bulk shipping'
  },
  {
    id: '6',
    email: '<EMAIL>',
    fullName: 'Customer Support',
    role: 'employee',
    phone: '+****************',
    company: 'LibyanoEx',
    isActive: true,
    subscriptionPlan: 'none',
    packagesCount: 0,
    totalSpent: 0,
    isVerified: true,
    createdAt: '2024-08-15T09:00:00Z',
    lastLogin: '2025-01-13T10:45:00Z',
    address: { city: 'Miami', state: 'FL', country: 'USA' }
  }
]

export default function UsersPage() {
  const { user, isLoading, hasAccess } = useRequireRole(['admin', 'manager'])
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to access this page.
          </p>
        </div>
      </DashboardLayout>
    )
  }

  const filteredUsers = mockUsers.filter(user => {
    const matchesSearch = user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.company.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && user.isActive) ||
                         (statusFilter === 'inactive' && !user.isActive)
    return matchesSearch && matchesRole && matchesStatus
  })

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800'
      case 'manager':
        return 'bg-blue-100 text-blue-800'
      case 'financial':
        return 'bg-green-100 text-green-800'
      case 'user':
        return 'bg-indigo-100 text-indigo-800'
      case 'client':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Users Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage user accounts and permissions
            </p>
          </div>
          {user?.role === 'admin' && (
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {mockUsers.filter(u => u.isActive).length}
              </div>
              <p className="text-xs text-muted-foreground">Active Users</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {mockUsers.filter(u => u.role === 'admin').length}
              </div>
              <p className="text-xs text-muted-foreground">Administrators</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {mockUsers.filter(u => u.role === 'manager').length}
              </div>
              <p className="text-xs text-muted-foreground">Managers</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {mockUsers.filter(u => !u.isActive).length}
              </div>
              <p className="text-xs text-muted-foreground">Inactive Users</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Roles</option>
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                  <option value="user">User</option>
                  <option value="client">Client</option>
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users List */}
        <Card>
          <CardHeader>
            <CardTitle>Users</CardTitle>
            <CardDescription>
              {filteredUsers.length} users found
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
                      {getInitials(user.fullName)}
                    </div>
                    <div>
                      <p className="font-medium">{user.fullName}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center">
                          <Mail className="mr-1 h-3 w-3" />
                          {user.email}
                        </span>
                        <span className="flex items-center">
                          <Phone className="mr-1 h-3 w-3" />
                          {user.phone}
                        </span>
                        <span className="flex items-center">
                          <Calendar className="mr-1 h-3 w-3" />
                          {formatDate(user.createdAt)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500">{user.company}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getRoleColor(user.role)}>
                      {user.role}
                    </Badge>
                    <Badge className={user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {user.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredUsers.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  No users found matching your criteria.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
