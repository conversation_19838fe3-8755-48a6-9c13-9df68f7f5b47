import { TrackingStatus } from '@/types'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  Truck, 
  MapPin, 
  CheckCircle, 
  AlertTriangle, 
  Clock,
  RotateCcw
} from 'lucide-react'

interface TrackingStatusProps {
  status: TrackingStatus
  statusDescription: string
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
}

/**
 * Tracking Status Component
 * 
 * Displays the current status of a package with appropriate styling and icons
 */
export default function TrackingStatusComponent({ 
  status, 
  statusDescription, 
  showIcon = true,
  size = 'md' 
}: TrackingStatusProps) {
  const getStatusConfig = (status: TrackingStatus) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
          icon: Clock,
          iconColor: 'text-gray-600'
        }
      case 'received':
        return {
          label: 'Received',
          color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
          icon: Package,
          iconColor: 'text-blue-600'
        }
      case 'processing':
        return {
          label: 'Processing',
          color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
          icon: RotateCcw,
          iconColor: 'text-yellow-600'
        }
      case 'in_transit':
        return {
          label: 'In Transit',
          color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
          icon: Truck,
          iconColor: 'text-purple-600'
        }
      case 'out_for_delivery':
        return {
          label: 'Out for Delivery',
          color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
          icon: MapPin,
          iconColor: 'text-orange-600'
        }
      case 'delivered':
        return {
          label: 'Delivered',
          color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
          icon: CheckCircle,
          iconColor: 'text-green-600'
        }
      case 'exception':
        return {
          label: 'Exception',
          color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
          icon: AlertTriangle,
          iconColor: 'text-red-600'
        }
      case 'returned':
        return {
          label: 'Returned',
          color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
          icon: RotateCcw,
          iconColor: 'text-gray-600'
        }
      default:
        return {
          label: 'Unknown',
          color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
          icon: Package,
          iconColor: 'text-gray-600'
        }
    }
  }

  const config = getStatusConfig(status)
  const Icon = config.icon

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  return (
    <div className="flex items-center space-x-2">
      {showIcon && (
        <Icon className={`${iconSizes[size]} ${config.iconColor}`} />
      )}
      <Badge className={`${config.color} ${sizeClasses[size]} font-medium`}>
        {config.label}
      </Badge>
      {statusDescription && size !== 'sm' && (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {statusDescription}
        </span>
      )}
    </div>
  )
}
