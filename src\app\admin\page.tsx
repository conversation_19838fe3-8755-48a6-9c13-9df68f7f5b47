/**
 * Admin Dashboard Page - Administrative Control Center
 * 
 * Comprehensive admin interface for LibyanoEx shipping forwarding portal
 * 
 * Features:
 * - Business metrics and analytics
 * - User management overview
 * - Package processing workflow
 * - Revenue and financial tracking
 * - System health monitoring
 * - Quick administrative actions
 * - Real-time notifications
 * - Performance insights
 * 
 * Access Control:
 * - Admin role required
 * - Permission-based feature access
 * - Audit logging for all actions
 */

'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Package, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Globe,
  Shield,
  Settings,
  BarChart3,
  PieChart,
  Activity,
  Bell,
  Download,
  RefreshCw,
  Eye,
  Edit,
  UserPlus,
  PackagePlus
} from 'lucide-react'

// Types for admin data
interface AdminMetrics {
  totalUsers: number
  activeUsers: number
  newUsersToday: number
  totalPackages: number
  packagesInTransit: number
  packagesDelivered: number
  monthlyRevenue: number
  revenueGrowth: number
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical'
}

interface RecentActivity {
  id: string
  type: 'user_registration' | 'package_created' | 'package_delivered' | 'payment_received' | 'system_alert'
  description: string
  timestamp: string
  severity: 'info' | 'success' | 'warning' | 'error'
}

interface TopCustomer {
  id: string
  name: string
  email: string
  packagesCount: number
  totalSpent: number
  lastActivity: string
  status: 'active' | 'inactive' | 'premium'
}

export default function AdminDashboardPage() {
  const { user } = useAuth()
  
  // State management
  const [isLoading, setIsLoading] = useState(false)
  const [selectedTimeRange, setSelectedTimeRange] = useState<'today' | 'week' | 'month' | 'year'>('month')
  
  // Mock admin data - in real app, this would come from API
  const adminMetrics: AdminMetrics = {
    totalUsers: 1247,
    activeUsers: 892,
    newUsersToday: 23,
    totalPackages: 5634,
    packagesInTransit: 234,
    packagesDelivered: 4987,
    monthlyRevenue: 45678.90,
    revenueGrowth: 12.5,
    systemHealth: 'excellent'
  }

  const recentActivity: RecentActivity[] = [
    {
      id: 'act-001',
      type: 'user_registration',
      description: 'New user registered: <EMAIL>',
      timestamp: '2025-01-13T10:30:00Z',
      severity: 'info'
    },
    {
      id: 'act-002',
      type: 'package_delivered',
      description: 'Package LBX-*********** delivered successfully',
      timestamp: '2025-01-13T10:15:00Z',
      severity: 'success'
    },
    {
      id: 'act-003',
      type: 'payment_received',
      description: 'Payment received: $89.99 from Premium subscription',
      timestamp: '2025-01-13T09:45:00Z',
      severity: 'success'
    },
    {
      id: 'act-004',
      type: 'system_alert',
      description: 'High API usage detected for DHL integration',
      timestamp: '2025-01-13T09:30:00Z',
      severity: 'warning'
    },
    {
      id: 'act-005',
      type: 'package_created',
      description: 'New package created: LBX-***********',
      timestamp: '2025-01-13T09:00:00Z',
      severity: 'info'
    }
  ]

  const topCustomers: TopCustomer[] = [
    {
      id: 'cust-001',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      packagesCount: 47,
      totalSpent: 2340.50,
      lastActivity: '2025-01-13',
      status: 'premium'
    },
    {
      id: 'cust-002',
      name: 'Michael Chen',
      email: '<EMAIL>',
      packagesCount: 32,
      totalSpent: 1890.25,
      lastActivity: '2025-01-12',
      status: 'active'
    },
    {
      id: 'cust-003',
      name: 'Emma Wilson',
      email: '<EMAIL>',
      packagesCount: 28,
      totalSpent: 1567.80,
      lastActivity: '2025-01-11',
      status: 'active'
    },
    {
      id: 'cust-004',
      name: 'David Rodriguez',
      email: '<EMAIL>',
      packagesCount: 25,
      totalSpent: 1234.75,
      lastActivity: '2025-01-10',
      status: 'premium'
    }
  ]

  /**
   * Gets the appropriate icon for activity type
   */
  const getActivityIcon = (type: RecentActivity['type']) => {
    const icons = {
      user_registration: UserPlus,
      package_created: PackagePlus,
      package_delivered: CheckCircle,
      payment_received: DollarSign,
      system_alert: AlertTriangle
    }
    return icons[type] || Activity
  }

  /**
   * Gets the appropriate color for activity severity
   */
  const getActivityColor = (severity: RecentActivity['severity']): string => {
    const colors = {
      info: 'text-blue-600',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      error: 'text-red-600'
    }
    return colors[severity] || 'text-gray-600'
  }

  /**
   * Gets system health color and status
   */
  const getSystemHealthStatus = (health: AdminMetrics['systemHealth']) => {
    const statuses = {
      excellent: { color: 'text-green-600', bg: 'bg-green-100', label: 'Excellent' },
      good: { color: 'text-blue-600', bg: 'bg-blue-100', label: 'Good' },
      warning: { color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Warning' },
      critical: { color: 'text-red-600', bg: 'bg-red-100', label: 'Critical' }
    }
    return statuses[health]
  }

  /**
   * Handles data refresh
   */
  const handleRefresh = async () => {
    setIsLoading(true)
    // TODO: Implement data refresh
    setTimeout(() => setIsLoading(false), 1000)
  }

  /**
   * Formats currency
   */
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  /**
   * Formats relative time
   */
  const formatRelativeTime = (timestamp: string): string => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`
    }
  }

  const systemHealthStatus = getSystemHealthStatus(adminMetrics.systemHealth)

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Admin Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Monitor and manage your LibyanoEx shipping operations
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center space-x-2">
            {/* Time Range Selector */}
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800"
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
            </select>
            
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* System Health Alert */}
        <Card className={`border-l-4 ${
          adminMetrics.systemHealth === 'excellent' ? 'border-l-green-500' :
          adminMetrics.systemHealth === 'good' ? 'border-l-blue-500' :
          adminMetrics.systemHealth === 'warning' ? 'border-l-yellow-500' :
          'border-l-red-500'
        }`}>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${systemHealthStatus.bg}`}>
                  <Shield className={`h-5 w-5 ${systemHealthStatus.color}`} />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    System Health: {systemHealthStatus.label}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    All systems operational. Last check: {new Date().toLocaleTimeString()}
                  </p>
                </div>
              </div>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                System Settings
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Users */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Users
              </CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {adminMetrics.totalUsers.toLocaleString()}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                <span className="text-green-600">+{adminMetrics.newUsersToday}</span> new today
              </p>
              <div className="mt-2">
                <div className="text-xs text-gray-500">
                  {adminMetrics.activeUsers} active users
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                  <div
                    className="bg-blue-600 h-1.5 rounded-full"
                    style={{ width: `${(adminMetrics.activeUsers / adminMetrics.totalUsers) * 100}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Total Packages */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Packages
              </CardTitle>
              <Package className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {adminMetrics.totalPackages.toLocaleString()}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                <span className="text-blue-600">{adminMetrics.packagesInTransit}</span> in transit
              </p>
              <div className="mt-2">
                <div className="text-xs text-gray-500">
                  {adminMetrics.packagesDelivered} delivered
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                  <div
                    className="bg-purple-600 h-1.5 rounded-full"
                    style={{ width: `${(adminMetrics.packagesDelivered / adminMetrics.totalPackages) * 100}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Monthly Revenue */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Monthly Revenue
              </CardTitle>
              <DollarSign className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(adminMetrics.monthlyRevenue)}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                <span className="text-green-600">+{adminMetrics.revenueGrowth}%</span> from last month
              </p>
              <div className="mt-2">
                <div className="text-xs text-gray-500">
                  Growth trend
                </div>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
                  <span className="text-xs text-green-600">Increasing</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Performance */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                System Performance
              </CardTitle>
              <Activity className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                99.9%
              </div>
              <p className="text-xs text-gray-500 mt-1">
                <span className="text-green-600">Uptime</span> this month
              </p>
              <div className="mt-2">
                <div className="text-xs text-gray-500">
                  Response time: 120ms
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                  <div className="bg-orange-600 h-1.5 rounded-full" style={{ width: '95%' }}></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center space-x-2">
                      <Bell className="h-5 w-5 text-blue-600" />
                      <span>Recent Activity</span>
                    </CardTitle>
                    <CardDescription>
                      Latest system events and user activities
                    </CardDescription>
                  </div>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => {
                    const ActivityIcon = getActivityIcon(activity.type)
                    const activityColor = getActivityColor(activity.severity)

                    return (
                      <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <div className={`p-2 rounded-full bg-gray-100 dark:bg-gray-800`}>
                          <ActivityIcon className={`h-4 w-4 ${activityColor}`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-gray-900 dark:text-white">
                            {activity.description}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatRelativeTime(activity.timestamp)}
                          </p>
                        </div>
                        <Badge
                          variant={activity.severity === 'success' ? 'default' : 'secondary'}
                          className={`text-xs ${
                            activity.severity === 'success' ? 'bg-green-100 text-green-800' :
                            activity.severity === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                            activity.severity === 'error' ? 'bg-red-100 text-red-800' :
                            'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {activity.severity}
                        </Badge>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Top Customers */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-purple-600" />
                  <span>Top Customers</span>
                </CardTitle>
                <CardDescription>
                  Highest value customers this month
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topCustomers.map((customer, index) => (
                    <div key={customer.id} className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                          {index + 1}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {customer.name}
                          </p>
                          <Badge
                            variant={customer.status === 'premium' ? 'default' : 'secondary'}
                            className={`text-xs ${
                              customer.status === 'premium' ? 'bg-gold-100 text-gold-800' : ''
                            }`}
                          >
                            {customer.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 truncate">
                          {customer.email}
                        </p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-gray-500">
                            {customer.packagesCount} packages
                          </span>
                          <span className="text-xs font-medium text-gray-900 dark:text-white">
                            {formatCurrency(customer.totalSpent)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button variant="outline" size="sm" className="w-full">
                    <Users className="h-4 w-4 mr-2" />
                    Manage All Users
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-blue-600" />
              <span>Quick Actions</span>
            </CardTitle>
            <CardDescription>
              Common administrative tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <Button variant="outline" className="h-20 flex-col space-y-2">
                <UserPlus className="h-6 w-6 text-blue-600" />
                <span className="text-sm">Add User</span>
              </Button>

              <Button variant="outline" className="h-20 flex-col space-y-2">
                <PackagePlus className="h-6 w-6 text-purple-600" />
                <span className="text-sm">Create Package</span>
              </Button>

              <Button variant="outline" className="h-20 flex-col space-y-2">
                <BarChart3 className="h-6 w-6 text-green-600" />
                <span className="text-sm">View Reports</span>
              </Button>

              <Button variant="outline" className="h-20 flex-col space-y-2">
                <Settings className="h-6 w-6 text-orange-600" />
                <span className="text-sm">System Config</span>
              </Button>

              <Button variant="outline" className="h-20 flex-col space-y-2">
                <Globe className="h-6 w-6 text-indigo-600" />
                <span className="text-sm">API Settings</span>
              </Button>

              <Button variant="outline" className="h-20 flex-col space-y-2">
                <Download className="h-6 w-6 text-gray-600" />
                <span className="text-sm">Export Data</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
