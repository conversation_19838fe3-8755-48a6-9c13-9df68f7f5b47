'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'

// Re-export the useAuth hook from the auth context
export { useAuth } from '@/contexts/auth-context'

export function useRequireAuth(redirectTo: string = '/auth/login') {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !user) {
      router.push(redirectTo)
    }
  }, [user, isLoading, router, redirectTo])

  return { user, isLoading }
}

export function useRequireRole(allowedRoles: string[], redirectTo: string = '/') {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && user && !allowedRoles.includes(user.role)) {
      router.push(redirectTo)
    }
  }, [user, isLoading, allowedRoles, router, redirectTo])

  return { user, isLoading, hasAccess: user ? allowedRoles.includes(user.role) : false }
}
