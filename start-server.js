const { spawn } = require('child_process');
const http = require('http');

function checkPort(port) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 2000
    }, (res) => {
      resolve(true);
    });

    req.on('error', () => resolve(false));
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function startServer() {
  console.log('Starting LibyanoEx Development Server...');
  
  // Try different ports
  const ports = [3000, 3001, 3002, 3004, 3005];
  
  for (const port of ports) {
    console.log(`Trying port ${port}...`);
    
    const isPortBusy = await checkPort(port);
    if (isPortBusy) {
      console.log(`Port ${port} is already in use, trying next port...`);
      continue;
    }
    
    console.log(`Starting Next.js on port ${port}...`);
    
    const nextProcess = spawn('npx', ['next', 'dev', '-p', port.toString()], {
      stdio: 'inherit',
      shell: true
    });
    
    nextProcess.on('error', (err) => {
      console.error(`Failed to start server on port ${port}:`, err.message);
    });
    
    // Wait a bit and check if server started
    setTimeout(async () => {
      const isRunning = await checkPort(port);
      if (isRunning) {
        console.log(`✅ Server is running at http://localhost:${port}`);
        console.log(`🏠 Homepage: http://localhost:${port}`);
        console.log(`🧪 Test page: http://localhost:${port}/test`);
      } else {
        console.log(`❌ Server failed to start on port ${port}`);
      }
    }, 5000);
    
    break;
  }
}

startServer();
