const http = require('http');

function checkServer(port) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Server is running on port ${port}`);
      console.log(`Status: ${res.statusCode}`);
      console.log(`URL: http://localhost:${port}`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ Server is NOT running on port ${port}`);
      console.log(`Error: ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`⏰ Timeout connecting to port ${port}`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function checkPorts() {
  console.log('🔍 Checking server status...\n');
  const ports = [3000, 3001, 3002, 3003, 8080];
  
  let foundRunning = false;
  for (const port of ports) {
    const isRunning = await checkServer(port);
    if (isRunning) {
      foundRunning = true;
    }
    console.log(''); // Add spacing
  }
  
  if (!foundRunning) {
    console.log('❌ No development server found running on any port');
    console.log('💡 Try running: npm run dev');
  }
}

checkPorts();
