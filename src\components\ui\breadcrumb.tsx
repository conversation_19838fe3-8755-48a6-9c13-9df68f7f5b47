import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'

interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  showHome?: boolean
}

/**
 * Breadcrumb Navigation Component
 * 
 * Provides hierarchical navigation with proper accessibility
 */
export default function Breadcrumb({ items, showHome = true }: BreadcrumbProps) {
  const allItems = showHome 
    ? [{ label: 'Dashboard', href: '/dashboard' }, ...items]
    : items

  return (
    <nav aria-label="Breadcrumb" className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
      {showHome && (
        <>
          <Link 
            href="/dashboard"
            className="flex items-center hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
          >
            <Home className="h-4 w-4" />
            <span className="sr-only">Dashboard</span>
          </Link>
          {allItems.length > 1 && <ChevronRight className="h-4 w-4" />}
        </>
      )}
      
      {allItems.slice(showHome ? 1 : 0).map((item, index) => {
        const isLast = index === allItems.slice(showHome ? 1 : 0).length - 1
        
        return (
          <div key={index} className="flex items-center space-x-1">
            {item.href && !item.current ? (
              <Link 
                href={item.href}
                className="hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
              >
                {item.label}
              </Link>
            ) : (
              <span className={item.current ? 'text-gray-900 dark:text-white font-medium' : ''}>
                {item.label}
              </span>
            )}
            
            {!isLast && <ChevronRight className="h-4 w-4" />}
          </div>
        )
      })}
    </nav>
  )
}
