import { useState } from 'react'
import { PackageTracking } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Bell, 
  Mail, 
  MessageSquare, 
  Smartphone,
  CheckCircle,
  Settings
} from 'lucide-react'

interface TrackingNotificationsProps {
  package: PackageTracking
}

/**
 * Tracking Notifications Component
 * 
 * Allows users to set up notifications for package status updates
 */
export default function TrackingNotifications({ package: pkg }: TrackingNotificationsProps) {
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [smsNotifications, setSmsNotifications] = useState(false)
  const [pushNotifications, setPushNotifications] = useState(true)
  const [email, setEmail] = useState('')
  const [phone, setPhone] = useState('')
  const [isSubscribed, setIsSubscribed] = useState(false)

  const handleSubscribe = () => {
    // In a real app, this would make an API call to set up notifications
    setIsSubscribed(true)
    setTimeout(() => setIsSubscribed(false), 3000) // Reset after 3 seconds for demo
  }

  const notificationTypes = [
    {
      id: 'status_updates',
      title: 'Status Updates',
      description: 'Get notified when your package status changes',
      enabled: true
    },
    {
      id: 'delivery_updates',
      title: 'Delivery Updates',
      description: 'Notifications about delivery attempts and completion',
      enabled: true
    },
    {
      id: 'exception_alerts',
      title: 'Exception Alerts',
      description: 'Immediate alerts for any delivery issues or delays',
      enabled: true
    },
    {
      id: 'delivery_reminders',
      title: 'Delivery Reminders',
      description: 'Reminders before scheduled delivery',
      enabled: false
    }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Bell className="h-5 w-5 text-blue-600" />
          <span>Tracking Notifications</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Notification Methods */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Notification Methods
          </h4>
          <div className="space-y-3">
            {/* Email Notifications */}
            <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Email Notifications
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Receive updates via email
                  </p>
                </div>
              </div>
              <Switch
                checked={emailNotifications}
                onCheckedChange={setEmailNotifications}
              />
            </div>

            {/* SMS Notifications */}
            <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <MessageSquare className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    SMS Notifications
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Get text message updates
                  </p>
                </div>
              </div>
              <Switch
                checked={smsNotifications}
                onCheckedChange={setSmsNotifications}
              />
            </div>

            {/* Push Notifications */}
            <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <Smartphone className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Push Notifications
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Browser and mobile app alerts
                  </p>
                </div>
              </div>
              <Switch
                checked={pushNotifications}
                onCheckedChange={setPushNotifications}
              />
            </div>
          </div>
        </div>

        {/* Contact Information */}
        {(emailNotifications || smsNotifications) && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Contact Information
            </h4>
            <div className="space-y-3">
              {emailNotifications && (
                <div>
                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400">
                    Email Address
                  </label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="mt-1"
                  />
                </div>
              )}
              
              {smsNotifications && (
                <div>
                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400">
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    placeholder="+****************"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="mt-1"
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Notification Types */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Notification Types
          </h4>
          <div className="space-y-2">
            {notificationTypes.map((type) => (
              <div key={type.id} className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {type.title}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {type.description}
                  </p>
                </div>
                <Switch defaultChecked={type.enabled} />
              </div>
            ))}
          </div>
        </div>

        {/* Subscribe Button */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          {isSubscribed ? (
            <div className="flex items-center space-x-2 text-green-600">
              <CheckCircle className="h-5 w-5" />
              <span className="text-sm font-medium">
                Notifications enabled for {pkg.trackingNumber}
              </span>
            </div>
          ) : (
            <Button 
              onClick={handleSubscribe}
              className="w-full"
              disabled={!emailNotifications && !smsNotifications && !pushNotifications}
            >
              <Bell className="h-4 w-4 mr-2" />
              Enable Notifications
            </Button>
          )}
        </div>

        {/* Current Alerts */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Active Alerts
          </h4>
          <div className="space-y-2">
            {pkg.status === 'out_for_delivery' && (
              <div className="flex items-center space-x-2 p-2 bg-orange-50 dark:bg-orange-900/20 rounded">
                <Bell className="h-4 w-4 text-orange-600" />
                <span className="text-sm text-orange-800 dark:text-orange-200">
                  Package out for delivery today
                </span>
              </div>
            )}
            
            {pkg.hasException && (
              <div className="flex items-center space-x-2 p-2 bg-red-50 dark:bg-red-900/20 rounded">
                <Bell className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-800 dark:text-red-200">
                  Delivery exception: {pkg.exceptionReason}
                </span>
              </div>
            )}
            
            {!pkg.hasException && pkg.status !== 'delivered' && (
              <div className="flex items-center space-x-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                <Bell className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-800 dark:text-blue-200">
                  Package tracking active
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Privacy Note */}
        <div className="text-xs text-gray-500 dark:text-gray-400 p-3 bg-gray-50 dark:bg-gray-800 rounded">
          <p>
            <strong>Privacy:</strong> Your contact information is used only for package notifications 
            and will not be shared with third parties. You can unsubscribe at any time.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
