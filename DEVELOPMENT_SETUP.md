# LibyanoEx Development Setup Guide

This guide will help you set up the LibyanoEx web application system for development.

## Quick Start (Development Mode)

The application is configured to work out of the box in development mode with mock data. You can start developing immediately without setting up Supabase.

### 1. Install Dependencies

```bash
npm install
```

### 2. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000` with:
- Mock authentication (automatically logged in as admin user)
- Sample data for transactions, users, and reports
- All features functional for development

## Production Setup with Supabase

For production or to test with real authentication and database, follow these steps:

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be fully provisioned
3. Go to Settings > API to get your project credentials

### 2. Configure Environment Variables

1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_actual_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key
   ```

### 3. Set Up Database Schema

1. Open Supabase SQL Editor in your project dashboard
2. Run the SQL files in this order:
   - `database/schema.sql` - Creates tables and indexes
   - `database/functions.sql` - Creates utility functions
   - `database/rls-policies.sql` - Sets up security policies
   - `database/seed-data.sql` - Adds initial configuration data

### 4. Configure Authentication

1. In Supabase dashboard, go to Authentication > Settings
2. Configure your site URL: `http://localhost:3000` (for development)
3. Add redirect URLs if needed
4. Enable email confirmations if desired

### 5. Test the Setup

1. Restart your development server:
   ```bash
   npm run dev
   ```

2. Visit `http://localhost:3000`
3. Try registering a new account
4. Check that authentication and database operations work

## Features Available

### Development Mode (No Supabase)
- ✅ Full UI/UX experience
- ✅ Mock authentication (admin user)
- ✅ Sample data for all features
- ✅ All pages and components functional
- ❌ Real user registration/login
- ❌ Data persistence
- ❌ Real-time features

### Production Mode (With Supabase)
- ✅ Real authentication system
- ✅ Data persistence in PostgreSQL
- ✅ User registration and login
- ✅ Role-based access control
- ✅ Real-time updates
- ✅ Secure data access with RLS

## Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Main dashboard
│   ├── transactions/      # Transaction management
│   ├── users/            # User management (admin)
│   └── reports/          # Analytics and reports
├── components/            # Reusable components
│   ├── ui/               # Basic UI components
│   ├── forms/            # Form components
│   └── layout/           # Layout components
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── stores/               # Zustand state stores
├── types/                # TypeScript type definitions
└── utils/                # Utility functions

database/                 # Database schema and setup
├── schema.sql           # Main database schema
├── functions.sql        # Custom database functions
├── rls-policies.sql     # Row Level Security policies
├── seed-data.sql        # Initial data
└── setup.md            # Database setup guide
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Technology Stack

- **Frontend**: Next.js 14, React 19, TypeScript
- **Styling**: Tailwind CSS, Shadcn/ui components
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod validation
- **Icons**: Lucide React

## Troubleshooting

### Common Issues

1. **"Invalid URL" error**: Make sure your environment variables are set correctly
2. **Authentication not working**: Check Supabase project settings and URLs
3. **Database errors**: Ensure all SQL scripts have been run in the correct order
4. **Build errors**: Check TypeScript types and imports

### Getting Help

1. Check the console for detailed error messages
2. Verify environment variables are set correctly
3. Ensure Supabase project is properly configured
4. Check database schema is set up correctly

## Next Steps

Once you have the basic setup working:

1. Customize the branding and styling
2. Add additional business logic
3. Implement real-time features
4. Set up deployment pipeline
5. Add comprehensive testing
6. Configure monitoring and analytics

For more detailed information, see the individual documentation files in the `database/` directory.
