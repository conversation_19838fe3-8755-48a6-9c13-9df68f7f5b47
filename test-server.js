const http = require('http');

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(`
    <html>
      <head><title>Test Server</title></head>
      <body>
        <h1>Test Server is Working!</h1>
        <p>This is a simple Node.js server running on port 3003</p>
        <p>If you can see this, Node.js is working properly.</p>
        <p>Time: ${new Date().toLocaleString()}</p>
        <p>URL: ${req.url}</p>
      </body>
    </html>
  `);
});

const PORT = 3003;
server.listen(PORT, () => {
  console.log(`Test server running at http://localhost:${PORT}`);
});
