import { PackageDetails, DeliveryInfo } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  Weight, 
  Ruler, 
  DollarSign, 
  Truck,
  MapPin,
  Clock,
  User,
  FileText,
  AlertCircle
} from 'lucide-react'

interface PackageDetailsProps {
  packageDetails: PackageDetails
  deliveryInfo: DeliveryInfo
  trackingNumber: string
  referenceNumber?: string
  orderId?: string
}

/**
 * Package Details Component
 * 
 * Displays comprehensive package information including dimensions, delivery details, etc.
 */
export default function PackageDetailsComponent({ 
  packageDetails, 
  deliveryInfo, 
  trackingNumber,
  referenceNumber,
  orderId
}: PackageDetailsProps) {
  const formatDimensions = () => {
    const { length, width, height, unit } = packageDetails.dimensions
    return `${length} × ${width} × ${height} ${unit}`
  }

  const formatWeight = () => {
    return `${packageDetails.weight} ${packageDetails.weightUnit}`
  }

  const formatAddress = () => {
    const addr = deliveryInfo.deliveryAddress
    return `${addr.street}, ${addr.city}, ${addr.state} ${addr.postalCode}, ${addr.country}`
  }

  const formatEstimatedDelivery = () => {
    const date = new Date(deliveryInfo.estimatedDate)
    const dateStr = date.toLocaleDateString('en-US', { 
      weekday: 'long',
      year: 'numeric',
      month: 'long', 
      day: 'numeric' 
    })
    
    if (deliveryInfo.estimatedTimeWindow) {
      return `${dateStr}, ${deliveryInfo.estimatedTimeWindow}`
    }
    return dateStr
  }

  return (
    <div className="space-y-6">
      {/* Package Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-blue-600" />
            <span>Package Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Tracking Numbers */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Tracking Number
              </label>
              <p className="text-sm font-mono text-gray-900 dark:text-white">
                {trackingNumber}
              </p>
            </div>
            {referenceNumber && (
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Reference Number
                </label>
                <p className="text-sm font-mono text-gray-900 dark:text-white">
                  {referenceNumber}
                </p>
              </div>
            )}
            {orderId && (
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Order ID
                </label>
                <p className="text-sm font-mono text-gray-900 dark:text-white">
                  {orderId}
                </p>
              </div>
            )}
          </div>

          {/* Physical Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Weight className="h-4 w-4 text-gray-500" />
              <div>
                <label className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  Weight
                </label>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatWeight()}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Ruler className="h-4 w-4 text-gray-500" />
              <div>
                <label className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  Dimensions
                </label>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatDimensions()}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-gray-500" />
              <div>
                <label className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  Declared Value
                </label>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {packageDetails.declaredValue} {packageDetails.currency}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Truck className="h-4 w-4 text-gray-500" />
              <div>
                <label className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  Service Type
                </label>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {packageDetails.serviceType}
                </p>
              </div>
            </div>
          </div>

          {/* Contents */}
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center space-x-1">
              <FileText className="h-4 w-4" />
              <span>Contents</span>
            </label>
            <p className="text-sm text-gray-900 dark:text-white mt-1">
              {packageDetails.contents}
            </p>
          </div>

          {/* Special Instructions */}
          {packageDetails.specialInstructions && (
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center space-x-1">
                <AlertCircle className="h-4 w-4" />
                <span>Special Instructions</span>
              </label>
              <p className="text-sm text-gray-900 dark:text-white mt-1">
                {packageDetails.specialInstructions}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delivery Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-green-600" />
            <span>Delivery Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Estimated Delivery */}
          <div className="flex items-start space-x-2">
            <Clock className="h-4 w-4 text-gray-500 mt-0.5" />
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Estimated Delivery
              </label>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {formatEstimatedDelivery()}
              </p>
            </div>
          </div>

          {/* Delivery Address */}
          <div className="flex items-start space-x-2">
            <User className="h-4 w-4 text-gray-500 mt-0.5" />
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Delivery Address
              </label>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {deliveryInfo.deliveryAddress.name}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {formatAddress()}
              </p>
            </div>
          </div>

          {/* Delivery Options */}
          <div className="flex flex-wrap gap-2">
            {deliveryInfo.requiresSignature && (
              <Badge variant="outline" className="text-xs">
                Signature Required
              </Badge>
            )}
            {deliveryInfo.isResidential && (
              <Badge variant="outline" className="text-xs">
                Residential Delivery
              </Badge>
            )}
          </div>

          {/* Delivery Instructions */}
          {deliveryInfo.deliveryInstructions && (
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Delivery Instructions
              </label>
              <p className="text-sm text-gray-900 dark:text-white mt-1">
                {deliveryInfo.deliveryInstructions}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
