'use client'

import { useAuth } from '@/hooks/use-auth'
import CustomerDashboardLayout from './customer-dashboard-layout'
import EmployeeDashboardLayout from './employee-dashboard-layout'
import FinancialDashboardLayout from './financial-dashboard-layout'
import AdminDashboardLayout from './admin-dashboard-layout'

interface RoleBasedDashboardLayoutProps {
  children: React.ReactNode
}

/**
 * Role-Based Dashboard Layout Router
 * 
 * Routes users to their appropriate dashboard layout based on their role:
 * - Customer Dashboard: 'user', 'client' roles
 * - Employee Dashboard: 'manager' role  
 * - Financial Manager Dashboard: 'financial' role
 * - Admin Dashboard: 'admin' role
 */
export default function RoleBasedDashboardLayout({ children }: RoleBasedDashboardLayoutProps) {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400">Please log in to access the dashboard.</p>
        </div>
      </div>
    )
  }

  // Route to appropriate dashboard based on user role
  switch (user.role) {
    case 'admin':
      return <AdminDashboardLayout>{children}</AdminDashboardLayout>
    
    case 'financial':
      return <FinancialDashboardLayout>{children}</FinancialDashboardLayout>
    
    case 'manager':
      return <EmployeeDashboardLayout>{children}</EmployeeDashboardLayout>
    
    case 'user':
    case 'client':
      return <CustomerDashboardLayout>{children}</CustomerDashboardLayout>
    
    default:
      // Fallback to customer dashboard for unknown roles
      return <CustomerDashboardLayout>{children}</CustomerDashboardLayout>
  }
}
