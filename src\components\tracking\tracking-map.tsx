import { TrackingLocation } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { MapPin, Navigation, Plane } from 'lucide-react'

interface TrackingMapProps {
  origin: TrackingLocation
  destination: TrackingLocation
  currentLocation: TrackingLocation
  isDelivered?: boolean
}

/**
 * Tracking Map Component
 * 
 * Displays a visual representation of package journey
 * This is a placeholder component - in production, integrate with actual map service
 */
export default function TrackingMap({ 
  origin, 
  destination, 
  currentLocation, 
  isDelivered = false 
}: TrackingMapProps) {
  const formatLocation = (location: TrackingLocation) => {
    const parts = [location.city]
    if (location.state) parts.push(location.state)
    parts.push(location.country)
    return parts.join(', ')
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Navigation className="h-5 w-5 text-blue-600" />
          <span>Package Journey</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Map Placeholder */}
        <div className="relative bg-gradient-to-br from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg h-64 mb-4 overflow-hidden">
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>

          {/* Route visualization */}
          <div className="absolute inset-4 flex items-center justify-between">
            {/* Origin */}
            <div className="flex flex-col items-center space-y-2">
              <div className="w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-lg"></div>
              <div className="text-xs text-center">
                <div className="font-medium text-gray-900 dark:text-white">Origin</div>
                <div className="text-gray-600 dark:text-gray-400">{origin.city}</div>
              </div>
            </div>

            {/* Route line */}
            <div className="flex-1 mx-4 relative">
              <div className="h-0.5 bg-gradient-to-r from-green-500 via-blue-500 to-orange-500 relative">
                {/* Current position indicator */}
                {!isDelivered && (
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
                  </div>
                )}
                
                {/* Plane icon for in-transit */}
                {!isDelivered && (
                  <div className="absolute top-1/2 left-1/3 transform -translate-x-1/2 -translate-y-1/2">
                    <Plane className="h-4 w-4 text-blue-600 animate-bounce" />
                  </div>
                )}
              </div>
            </div>

            {/* Destination */}
            <div className="flex flex-col items-center space-y-2">
              <div className={`w-4 h-4 rounded-full border-2 border-white shadow-lg ${
                isDelivered ? 'bg-green-500' : 'bg-orange-500'
              }`}></div>
              <div className="text-xs text-center">
                <div className="font-medium text-gray-900 dark:text-white">Destination</div>
                <div className="text-gray-600 dark:text-gray-400">{destination.city}</div>
              </div>
            </div>
          </div>

          {/* Map overlay message */}
          <div className="absolute bottom-2 left-2 right-2">
            <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded px-2 py-1 text-xs text-center text-gray-600 dark:text-gray-400">
              📍 Interactive map integration available in production
            </div>
          </div>
        </div>

        {/* Location Details */}
        <div className="space-y-3">
          {/* Origin */}
          <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  Origin
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {formatLocation(origin)}
                </div>
                {origin.facility && (
                  <div className="text-xs text-gray-500 dark:text-gray-500">
                    {origin.facility}
                  </div>
                )}
              </div>
            </div>
            <Badge variant="outline" className="text-xs">
              Shipped
            </Badge>
          </div>

          {/* Current Location */}
          {!isDelivered && (
            <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    Current Location
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {formatLocation(currentLocation)}
                  </div>
                  {currentLocation.facility && (
                    <div className="text-xs text-gray-500 dark:text-gray-500">
                      {currentLocation.facility}
                    </div>
                  )}
                </div>
              </div>
              <Badge className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                In Transit
              </Badge>
            </div>
          )}

          {/* Destination */}
          <div className="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${
                isDelivered ? 'bg-green-500' : 'bg-orange-500'
              }`}></div>
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  Destination
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {formatLocation(destination)}
                </div>
              </div>
            </div>
            <Badge variant="outline" className={`text-xs ${
              isDelivered 
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                : ''
            }`}>
              {isDelivered ? 'Delivered' : 'Pending'}
            </Badge>
          </div>
        </div>

        {/* Integration Note */}
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-start space-x-2">
            <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
            <div className="text-xs text-gray-600 dark:text-gray-400">
              <strong>Note:</strong> This is a simplified route visualization. 
              In production, this would integrate with mapping services like Google Maps 
              or Mapbox to show real-time package location and detailed route information.
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
