// User Types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  role: UserRole
  created_at: string
  updated_at: string
  is_active: boolean
}

export type UserRole = 'admin' | 'manager' | 'financial' | 'user' | 'client'

// Transaction Types
export interface Transaction {
  id: string
  user_id: string
  type: TransactionType
  amount: number
  currency: string
  status: TransactionStatus
  description?: string
  reference_number: string
  created_at: string
  updated_at: string
  metadata?: Record<string, any>
}

export type TransactionType = 'deposit' | 'withdrawal' | 'transfer' | 'exchange' | 'fee'
export type TransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled'

// Dashboard Types
export interface DashboardStats {
  totalUsers: number
  totalTransactions: number
  totalRevenue: number
  pendingTransactions: number
  monthlyGrowth: number
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Form Types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  password: string
  confirmPassword: string
  fullName: string
}

export interface TransactionForm {
  type: TransactionType
  amount: number
  currency: string
  description?: string
}

// Navigation Types
export interface NavItem {
  title: string
  href: string
  icon?: string
  disabled?: boolean
  external?: boolean
  label?: string
}

export interface SidebarNavItem extends NavItem {
  items?: SidebarNavItem[]
}
