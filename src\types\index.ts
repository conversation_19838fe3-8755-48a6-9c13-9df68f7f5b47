// User Types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  role: User<PERSON>ole
  created_at: string
  updated_at: string
  is_active: boolean
}

export type UserRole = 'admin' | 'manager' | 'financial' | 'user' | 'client'

// Transaction Types
export interface Transaction {
  id: string
  user_id: string
  type: TransactionType
  amount: number
  currency: string
  status: TransactionStatus
  description?: string
  reference_number: string
  created_at: string
  updated_at: string
  metadata?: Record<string, any>
}

export type TransactionType = 'deposit' | 'withdrawal' | 'transfer' | 'exchange' | 'fee'
export type TransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled'

// Dashboard Types
export interface DashboardStats {
  totalUsers: number
  totalTransactions: number
  totalRevenue: number
  pendingTransactions: number
  monthlyGrowth: number
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Form Types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  password: string
  confirmPassword: string
  fullName: string
}

export interface TransactionForm {
  type: TransactionType
  amount: number
  currency: string
  description?: string
}

// Navigation Types
export interface NavItem {
  title: string
  href: string
  icon?: string
  disabled?: boolean
  external?: boolean
  label?: string
}

export interface SidebarNavItem extends NavItem {
  items?: SidebarNavItem[]
}

// Package Tracking Types
export type TrackingStatus =
  | 'pending'
  | 'received'
  | 'processing'
  | 'in_transit'
  | 'out_for_delivery'
  | 'delivered'
  | 'exception'
  | 'returned'

export type EventType =
  | 'package_received'
  | 'processing_started'
  | 'consolidation_complete'
  | 'shipped'
  | 'in_transit'
  | 'customs_clearance'
  | 'out_for_delivery'
  | 'delivery_attempted'
  | 'delivered'
  | 'exception'
  | 'returned'

export interface TrackingEvent {
  id: string
  type: EventType
  status: TrackingStatus
  title: string
  description: string
  location: string
  timestamp: string
  isCompleted: boolean
  isActive: boolean
}

export interface PackageDetails {
  weight: number
  weightUnit: 'kg' | 'lbs'
  dimensions: {
    length: number
    width: number
    height: number
    unit: 'cm' | 'in'
  }
  serviceType: string
  declaredValue: number
  currency: string
  contents: string
  specialInstructions?: string
}

export interface DeliveryInfo {
  estimatedDate: string
  estimatedTimeWindow?: string
  deliveryAddress: {
    name: string
    street: string
    city: string
    state: string
    postalCode: string
    country: string
  }
  deliveryInstructions?: string
  requiresSignature: boolean
  isResidential: boolean
}

export interface TrackingLocation {
  latitude?: number
  longitude?: number
  city: string
  state?: string
  country: string
  facility?: string
}

export interface PackageTracking {
  id: string
  trackingNumber: string
  referenceNumber?: string
  orderId?: string
  status: TrackingStatus
  statusDescription: string
  currentLocation: TrackingLocation
  origin: TrackingLocation
  destination: TrackingLocation
  packageDetails: PackageDetails
  deliveryInfo: DeliveryInfo
  events: TrackingEvent[]
  createdAt: string
  updatedAt: string
  estimatedDelivery: string
  isDelivered: boolean
  hasException: boolean
  exceptionReason?: string
}

export interface TrackingSearchResult {
  success: boolean
  package?: PackageTracking
  error?: string
  suggestions?: string[]
}
